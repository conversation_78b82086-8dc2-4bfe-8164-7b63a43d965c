# Next.js Authentication App

A complete authentication system built with Next.js 15, NextAuth.js, and integrated with a custom backend API.

## 🚀 Features

### Authentication
- ✅ **Login/Logout** - Secure authentication with JWT tokens
- ✅ **Registration** - User signup with validation
- ✅ **Password Reset** - Forgot password with <PERSON>TP verification
- ✅ **Change Password** - Secure password updates
- ✅ **Email Verification** - Account verification system
- ✅ **Session Management** - Automatic token refresh
- ✅ **Protected Routes** - Route-level authentication guards

### User Management
- ✅ **Profile Management** - Complete user profile CRUD
- ✅ **Dashboard** - User overview and quick actions
- ✅ **Settings** - Account settings and preferences
- ✅ **Health Information** - Fitness and health data tracking

### UI/UX
- ✅ **Responsive Design** - Mobile-first approach with Tailwind CSS
- ✅ **Loading States** - Smooth user experience with loading indicators
- ✅ **Error Handling** - Comprehensive error messages and validation
- ✅ **Modern UI** - Clean and professional interface

## 🛠 Tech Stack

- **Frontend**: Next.js 15 (App Router), React 19, Tai<PERSON><PERSON> CSS
- **Authentication**: NextAuth.js v4
- **API Integration**: Custom REST API client
- **Styling**: Tailwind CSS v4
- **State Management**: React Hooks

## 🔧 Setup & Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Environment Configuration**
   Create a `.env.local` file:
   ```env
   # NextAuth.js Configuration
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-secret-key-here-change-this-in-production

   # API Configuration
   NEXT_PUBLIC_API_BASE_URL=http://localhost:8000/api
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Access the application**
   Open [http://localhost:3000](http://localhost:3000)

## 🔐 Authentication Flow

### Login Process
1. User enters credentials on `/login`
2. NextAuth.js sends credentials to backend API
3. Backend validates and returns user data + JWT tokens
4. NextAuth.js creates session with user data
5. User is redirected to dashboard

### Test Credentials
For testing, use these credentials:
- **Email**: `<EMAIL>`
- **Password**: `Test@123`

## 📡 API Integration

The app integrates with a custom backend API with the following endpoints:

### Authentication Endpoints
- `POST /auth/login` - User login
- `POST /auth/signup` - User registration
- `POST /auth/logout` - User logout
- `POST /auth/forgot-password` - Request password reset OTP
- `POST /auth/reset-password` - Reset password with OTP
- `POST /auth/change-password` - Change password (authenticated)
- `POST /auth/refresh-token` - Refresh access token
- `POST /auth/verify-email` - Verify email with OTP
- `POST /auth/send-verification-email` - Send verification email

### Profile Endpoints
- `GET /clients/profile/me` - Get user profile
- `PUT /clients/profile/me` - Update user profile

## 🎨 Available Pages

- **/** - Home page
- **/login** - Login form
- **/register** - Registration form
- **/forgot-password** - Password reset request
- **/reset-password** - Password reset with OTP
- **/dashboard** - User dashboard (protected)
- **/profile** - Profile management (protected)
- **/settings** - Account settings (protected)

## 🔒 Security Features

- **JWT Token Management** - Secure token storage and refresh
- **CSRF Protection** - Built-in CSRF protection with NextAuth.js
- **Input Validation** - Client and server-side validation
- **Route Protection** - Authentication guards on sensitive routes

## 🚀 Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start production server**
   ```bash
   npm start
   ```

3. **Environment Variables**
   Update `.env.local` for production:
   - Set `NEXTAUTH_URL` to your production domain
   - Generate a secure `NEXTAUTH_SECRET`
   - Update `NEXT_PUBLIC_API_BASE_URL` to your API endpoint
