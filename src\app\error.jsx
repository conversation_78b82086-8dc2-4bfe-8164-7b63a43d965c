"use client";

export default function Error({ error, reset }) {
    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="max-w-md w-full text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    Something went wrong!
                </h2>
                <p className="text-gray-600 mb-6">
                    {error?.message || "An unexpected error occurred"}
                </p>
                <button
                    onClick={reset}
                    className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors"
                >
                    Try again
                </button>
            </div>
        </div>
    );
}