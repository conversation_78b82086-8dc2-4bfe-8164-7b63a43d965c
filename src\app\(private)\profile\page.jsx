"use client";
import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { apiClient } from "@/lib/api";

export default function ProfilePage() {
    const { data: session } = useSession();
    const [profile, setProfile] = useState(null);
    const [formData, setFormData] = useState({
        firstName: "",
        lastName: "",
        phoneNumber: "",
        dateOfBirth: "",
        height: "",
        weight: "",
        current_weight: "",
        expected_weight: "",
        address: "",
        eating_preference: "",
        intoxication: [],
        medical_issues: [],
        stress_level: 1,
        eating_habits: {
            breakfast: "",
            lunch: "",
            dinner: "",
            snacks: ""
        },
        description: "",
        goals: []
    });
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState("");
    const [success, setSuccess] = useState("");

    useEffect(() => {
        if (session?.accessToken) {
            fetchProfile();
        }
    }, [session]);

    const fetchProfile = async () => {
        try {
            setLoading(true);
            const response = await apiClient.getProfile();
            if (response.status === "Success") {
                const profileData = response.data;
                setProfile(profileData);

                // Populate form with existing data
                setFormData({
                    firstName: profileData.firstName || "",
                    lastName: profileData.lastName || "",
                    phoneNumber: profileData.phoneNumber || "",
                    dateOfBirth: profileData.dateOfBirth ? profileData.dateOfBirth.split('T')[0] : "",
                    height: profileData.height || "",
                    weight: profileData.weight || "",
                    current_weight: profileData.current_weight || "",
                    expected_weight: profileData.expected_weight || "",
                    address: profileData.address || "",
                    eating_preference: profileData.eating_preference || "",
                    intoxication: profileData.intoxication || [],
                    medical_issues: profileData.medical_issues || [],
                    stress_level: profileData.stress_level || 1,
                    eating_habits: profileData.eating_habits || {
                        breakfast: "",
                        lunch: "",
                        dinner: "",
                        snacks: ""
                    },
                    description: profileData.description || "",
                    goals: profileData.goals || []
                });
            }
        } catch (error) {
            setError("Failed to load profile data");
            console.error("Profile fetch error:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;

        if (name.startsWith('eating_habits.')) {
            const habitKey = name.split('.')[1];
            setFormData(prev => ({
                ...prev,
                eating_habits: {
                    ...prev.eating_habits,
                    [habitKey]: value
                }
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }
    };

    const handleArrayChange = (field, value) => {
        const items = value.split(',').map(item => item.trim()).filter(item => item);
        setFormData(prev => ({
            ...prev,
            [field]: items
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSaving(true);
        setError("");
        setSuccess("");

        try {
            const response = await apiClient.updateProfile(formData);
            if (response.status === "Success") {
                setSuccess("Profile updated successfully!");
                await fetchProfile(); // Refresh profile data
            }
        } catch (error) {
            setError(error.message || "Failed to update profile");
        } finally {
            setSaving(false);
        }
    };

    if (loading) {
        return (
            <div className="max-w-4xl mx-auto">
                <div className="bg-white shadow rounded-lg p-6">
                    <div className="animate-pulse">
                        <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
                        <div className="space-y-4">
                            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="max-w-4xl mx-auto">
            <div className="bg-white shadow rounded-lg p-6">
                <h1 className="text-3xl font-bold text-gray-900 mb-6">Profile</h1>

                {error && (
                    <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md mb-6">
                        {error}
                    </div>
                )}

                {success && (
                    <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md mb-6">
                        {success}
                    </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Personal Information */}
                    <div>
                        <h2 className="text-xl font-semibold text-gray-900 mb-4">Personal Information</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                                    First Name
                                </label>
                                <input
                                    type="text"
                                    id="firstName"
                                    name="firstName"
                                    value={formData.firstName}
                                    onChange={handleChange}
                                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                />
                            </div>

                            <div>
                                <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                                    Last Name
                                </label>
                                <input
                                    type="text"
                                    id="lastName"
                                    name="lastName"
                                    value={formData.lastName}
                                    onChange={handleChange}
                                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                />
                            </div>

                            <div>
                                <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
                                    Phone Number
                                </label>
                                <input
                                    type="tel"
                                    id="phoneNumber"
                                    name="phoneNumber"
                                    value={formData.phoneNumber}
                                    onChange={handleChange}
                                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                />
                            </div>

                            <div>
                                <label htmlFor="dateOfBirth" className="block text-sm font-medium text-gray-700">
                                    Date of Birth
                                </label>
                                <input
                                    type="date"
                                    id="dateOfBirth"
                                    name="dateOfBirth"
                                    value={formData.dateOfBirth}
                                    onChange={handleChange}
                                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Health Information */}
                    <div>
                        <h2 className="text-xl font-semibold text-gray-900 mb-4">Health Information</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label htmlFor="height" className="block text-sm font-medium text-gray-700">
                                    Height (cm)
                                </label>
                                <input
                                    type="number"
                                    id="height"
                                    name="height"
                                    value={formData.height}
                                    onChange={handleChange}
                                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                />
                            </div>

                            <div>
                                <label htmlFor="current_weight" className="block text-sm font-medium text-gray-700">
                                    Current Weight (kg)
                                </label>
                                <input
                                    type="number"
                                    id="current_weight"
                                    name="current_weight"
                                    value={formData.current_weight}
                                    onChange={handleChange}
                                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                />
                            </div>

                            <div>
                                <label htmlFor="expected_weight" className="block text-sm font-medium text-gray-700">
                                    Target Weight (kg)
                                </label>
                                <input
                                    type="number"
                                    id="expected_weight"
                                    name="expected_weight"
                                    value={formData.expected_weight}
                                    onChange={handleChange}
                                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                />
                            </div>

                            <div>
                                <label htmlFor="eating_preference" className="block text-sm font-medium text-gray-700">
                                    Eating Preference
                                </label>
                                <select
                                    id="eating_preference"
                                    name="eating_preference"
                                    value={formData.eating_preference}
                                    onChange={handleChange}
                                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                >
                                    <option value="">Select preference</option>
                                    <option value="Vegetarian">Vegetarian</option>
                                    <option value="Non-Vegetarian">Non-Vegetarian</option>
                                    <option value="Vegan">Vegan</option>
                                    <option value="Keto">Keto</option>
                                    <option value="Paleo">Paleo</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    {/* Additional Information */}
                    <div>
                        <h2 className="text-xl font-semibold text-gray-900 mb-4">Additional Information</h2>
                        <div className="space-y-4">
                            <div>
                                <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                                    Address
                                </label>
                                <textarea
                                    id="address"
                                    name="address"
                                    rows={3}
                                    value={formData.address}
                                    onChange={handleChange}
                                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                />
                            </div>

                            <div>
                                <label htmlFor="medical_issues" className="block text-sm font-medium text-gray-700">
                                    Medical Issues (comma-separated)
                                </label>
                                <input
                                    type="text"
                                    id="medical_issues"
                                    name="medical_issues"
                                    value={formData.medical_issues.join(', ')}
                                    onChange={(e) => handleArrayChange('medical_issues', e.target.value)}
                                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    placeholder="e.g., Diabetes, Hypertension"
                                />
                            </div>

                            <div>
                                <label htmlFor="goals" className="block text-sm font-medium text-gray-700">
                                    Goals (comma-separated)
                                </label>
                                <input
                                    type="text"
                                    id="goals"
                                    name="goals"
                                    value={formData.goals.join(', ')}
                                    onChange={(e) => handleArrayChange('goals', e.target.value)}
                                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    placeholder="e.g., Lose weight, Build muscle"
                                />
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-end">
                        <button
                            type="submit"
                            disabled={saving}
                            className="bg-indigo-600 text-white px-6 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {saving ? "Saving..." : "Save Changes"}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}
