"use client";
import { useState, Suspense } from "react";
import dynamic from "next/dynamic";

const ProfileTab = dynamic(() => import("@/components/profile/ProfileTab"));
const SettingsTab = dynamic(() => import("@/components/profile/SettingsTab"));

export default function ProfilePage() {
    const [tab, setTab] = useState("profile");

    return (
        <div>
            <div className="tabs mb-4">
                <button onClick={() => setTab("profile")}>Profile</button>
                <button onClick={() => setTab("settings")}>Settings</button>
            </div>
            <Suspense fallback={<p>Loading tab...</p>}>
                {tab === "profile" ? <ProfileTab /> : <SettingsTab />}
            </Suspense>
        </div>
    );
}
