"use client";
import { useSession, signIn, signOut } from "next-auth/react";
import Link from "next/link";

export default function Header() {
    const { data: session } = useSession();
    return (
        <header className="p-4 border-b border-gray-200">
            <nav className="flex justify-between">
                <div className="space-x-4">
                    <Link href="/">Home</Link>
                    <Link href="/about">About</Link>
                    <Link href="/contact">Contact</Link>
                    {session && <Link href="/dashboard">Dashboard</Link>}
                </div>
                <div>
                    {!session ? (
                        <button onClick={() => signIn()}>Login</button>
                    ) : (
                        <button onClick={() => signOut()}>Logout</button>
                    )}
                </div>
            </nav>
        </header>
    );
}