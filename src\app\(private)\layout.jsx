import { auth } from "@/auth"; // Replace with actual auth session method if different
import { redirect } from "next/navigation";
import Header from "@/components/ui/Header";
import Footer from "@/components/ui/Footer";

export default async function PrivateLayout({ children }) {
    const session = await auth();
    if (!session) redirect("/login");

    return (
        <>
            <Header />
            <main className="p-4">{children}</main>
            <Footer />
        </>
    );
}
