"use client";
import { useSession, signOut } from "next-auth/react";
import { useState, useEffect } from "react";
import { apiClient } from "@/lib/api";
import Link from "next/link";

export default function DashboardPage() {
    const { data: session } = useSession();
    const [profile, setProfile] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState("");

    useEffect(() => {
        if (session?.accessToken) {
            fetchProfile();
        }
    }, [session]);

    const fetchProfile = async () => {
        try {
            setLoading(true);
            const response = await apiClient.getProfile();
            if (response.status === "Success") {
                setProfile(response.data);
            }
        } catch (error) {
            setError("Failed to load profile data");
            console.error("Profile fetch error:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleLogout = async () => {
        try {
            await apiClient.logout();
        } catch (error) {
            console.error("Logout error:", error);
        } finally {
            await signOut();
        }
    };

    if (loading) {
        return (
            <div className="max-w-4xl mx-auto">
                <div className="bg-white shadow rounded-lg p-6">
                    <div className="animate-pulse">
                        <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
                        <div className="space-y-4">
                            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="max-w-4xl mx-auto space-y-6">
            {/* Welcome Section */}
            <div className="bg-white shadow rounded-lg p-6">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">Dashboard</h1>

                {session && (
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <h2 className="text-lg font-semibold text-blue-800 mb-2">
                            Welcome back, {session.user?.firstName || session.user?.name || session.user?.email}!
                        </h2>
                        <p className="text-blue-700">
                            You are successfully authenticated and can access all features.
                        </p>
                        {!session.user?.isVerified && (
                            <div className="mt-2 p-2 bg-yellow-100 border border-yellow-300 rounded text-yellow-800 text-sm">
                                ⚠️ Your email is not verified. Please check your email for verification instructions.
                            </div>
                        )}
                    </div>
                )}
            </div>

            {/* Quick Actions */}
            <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Link
                        href="/profile"
                        className="bg-indigo-50 border border-indigo-200 rounded-lg p-4 hover:bg-indigo-100 transition-colors"
                    >
                        <h4 className="font-medium text-indigo-900">Profile</h4>
                        <p className="text-sm text-indigo-700">View and edit your profile</p>
                    </Link>

                    <Link
                        href="/settings"
                        className="bg-green-50 border border-green-200 rounded-lg p-4 hover:bg-green-100 transition-colors"
                    >
                        <h4 className="font-medium text-green-900">Settings</h4>
                        <p className="text-sm text-green-700">Manage your account settings</p>
                    </Link>

                    <button
                        onClick={handleLogout}
                        className="bg-red-50 border border-red-200 rounded-lg p-4 hover:bg-red-100 transition-colors text-left"
                    >
                        <h4 className="font-medium text-red-900">Sign Out</h4>
                        <p className="text-sm text-red-700">Securely log out of your account</p>
                    </button>
                </div>
            </div>

            {/* Profile Summary */}
            {profile && (
                <div className="bg-white shadow rounded-lg p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">Profile Summary</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 className="font-medium text-gray-900 mb-2">Personal Information</h4>
                            <div className="space-y-2 text-sm">
                                <p><span className="font-medium">Name:</span> {profile.firstName} {profile.lastName}</p>
                                <p><span className="font-medium">Email:</span> {profile.email}</p>
                                <p><span className="font-medium">Phone:</span> {profile.phoneNumber || "Not provided"}</p>
                                <p><span className="font-medium">Date of Birth:</span> {profile.dateOfBirth ? new Date(profile.dateOfBirth).toLocaleDateString() : "Not provided"}</p>
                            </div>
                        </div>

                        <div>
                            <h4 className="font-medium text-gray-900 mb-2">Account Status</h4>
                            <div className="space-y-2 text-sm">
                                <p>
                                    <span className="font-medium">Verified:</span>
                                    <span className={`ml-2 px-2 py-1 rounded text-xs ${profile.isVerified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                        {profile.isVerified ? 'Yes' : 'No'}
                                    </span>
                                </p>
                                <p>
                                    <span className="font-medium">Onboarded:</span>
                                    <span className={`ml-2 px-2 py-1 rounded text-xs ${profile.isOnboarded ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                                        {profile.isOnboarded ? 'Complete' : 'Pending'}
                                    </span>
                                </p>
                                <p><span className="font-medium">Role:</span> {profile.roles}</p>
                                <p><span className="font-medium">Member since:</span> {new Date(profile.createdAt).toLocaleDateString()}</p>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {error && (
                <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
                    {error}
                </div>
            )}
        </div>
    );
}
