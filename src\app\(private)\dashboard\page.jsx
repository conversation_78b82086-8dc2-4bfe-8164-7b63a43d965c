"use client";
import { useSession, signOut } from "next-auth/react";

export default function DashboardPage() {
    const { data: session } = useSession();

    return (
        <div className="max-w-4xl mx-auto">
            <div className="bg-white shadow rounded-lg p-6">
                <h1 className="text-3xl font-bold text-gray-900 mb-6">Dashboard</h1>

                {session ? (
                    <div className="space-y-4">
                        <div className="bg-green-50 border border-green-200 rounded-md p-4">
                            <h2 className="text-lg font-semibold text-green-800 mb-2">
                                Welcome, {session.user?.name || session.user?.email}!
                            </h2>
                            <p className="text-green-700">
                                You are successfully authenticated and can access protected routes.
                            </p>
                        </div>

                        <div className="bg-gray-50 rounded-md p-4">
                            <h3 className="font-semibold text-gray-800 mb-2">Session Information:</h3>
                            <pre className="text-sm text-gray-600 bg-white p-3 rounded border overflow-auto">
                                {JSON.stringify(session, null, 2)}
                            </pre>
                        </div>

                        <div className="flex space-x-4">
                            <button
                                onClick={() => signOut()}
                                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
                            >
                                Sign Out
                            </button>
                        </div>
                    </div>
                ) : (
                    <div className="text-center py-8">
                        <p className="text-gray-600">Loading session...</p>
                    </div>
                )}
            </div>
        </div>
    );
}
