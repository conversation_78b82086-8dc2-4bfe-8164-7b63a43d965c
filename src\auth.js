import NextAuth from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";

export const authOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        // Add your authentication logic here
        // This is a basic example - replace with your actual authentication
        if (credentials?.email === "<EMAIL>" && credentials?.password === "password") {
          return {
            id: "1",
            email: "<EMAIL>",
            name: "Admin User",
          };
        }
        return null;
      },
    }),
  ],
  pages: {
    signIn: "/login",
  },
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id;
      }
      return session;
    },
  },
};

const handler = NextAuth(authOptions);

export const handlers = { GET: handler, POST: handler };
export { handler as auth };
