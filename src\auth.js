import NextAuth from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000/api";

export const authOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        try {
          const response = await fetch(`${API_BASE_URL}/auth/login`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: credentials?.email,
              password: credentials?.password,
            }),
          });

          const data = await response.json();

          if (response.ok && data.status === "Success") {
            const userData = data.data;
            return {
              id: userData._id || userData.id,
              email: userData.email,
              name: `${userData.firstName || ""} ${userData.lastName || ""}`.trim(),
              firstName: userData.firstName,
              lastName: userData.lastName,
              phoneNumber: userData.phoneNumber,
              dateOfBirth: userData.dateOfBirth,
              avatar: userData.avatar,
              accessToken: userData.accessToken,
              refreshToken: userData.refreshToken,
              accessTokenExp: userData.accessTokenExp,
              refreshTokenExp: userData.refreshTokenExp,
              roles: userData.roles,
              isVerified: userData.isVerified,
              isOnboarded: userData.isOnboarded,
            };
          }

          console.error("Login failed:", data.message);
          return null;
        } catch (error) {
          console.error("Authentication error:", error);
          return null;
        }
      },
    }),
  ],
  pages: {
    signIn: "/login",
    error: "/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },
  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (account && user) {
        return {
          ...token,
          id: user.id,
          email: user.email,
          name: user.name,
          firstName: user.firstName,
          lastName: user.lastName,
          phoneNumber: user.phoneNumber,
          dateOfBirth: user.dateOfBirth,
          avatar: user.avatar,
          accessToken: user.accessToken,
          refreshToken: user.refreshToken,
          accessTokenExp: user.accessTokenExp,
          refreshTokenExp: user.refreshTokenExp,
          roles: user.roles,
          isVerified: user.isVerified,
          isOnboarded: user.isOnboarded,
        };
      }

      // Return previous token if the access token has not expired yet
      if (Date.now() < token.accessTokenExp * 1000) {
        return token;
      }

      // Access token has expired, try to update it
      return await refreshAccessToken(token);
    },
    async session({ session, token }) {
      if (token) {
        session.user = {
          id: token.id,
          email: token.email,
          name: token.name,
          firstName: token.firstName,
          lastName: token.lastName,
          phoneNumber: token.phoneNumber,
          dateOfBirth: token.dateOfBirth,
          avatar: token.avatar,
          roles: token.roles,
          isVerified: token.isVerified,
          isOnboarded: token.isOnboarded,
        };
        session.accessToken = token.accessToken;
        session.error = token.error;
      }
      return session;
    },
  },
};

async function refreshAccessToken(token) {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/refresh-token`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Cookie": `refreshToken=${token.refreshToken}`,
      },
    });

    const data = await response.json();

    if (response.ok && data.status === "Success") {
      const refreshedTokens = data.data;
      return {
        ...token,
        accessToken: refreshedTokens.accessToken,
        accessTokenExp: refreshedTokens.accessTokenExp,
        refreshToken: refreshedTokens.refreshToken ?? token.refreshToken,
        refreshTokenExp: refreshedTokens.refreshTokenExp ?? token.refreshTokenExp,
      };
    }

    return {
      ...token,
      error: "RefreshAccessTokenError",
    };
  } catch (error) {
    return {
      ...token,
      error: "RefreshAccessTokenError",
    };
  }
}

const handler = NextAuth(authOptions);

export const handlers = { GET: handler, POST: handler };
export { handler as auth };
