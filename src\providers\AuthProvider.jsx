"use client";
import { SessionProvider, useSession } from "next-auth/react";
import { usePathname, useRouter } from "next/navigation";
import { useEffect } from "react";

const publicRoutes = ["/", "/about", "/contact", "/login"];

export function AuthGuard({ children }) {
    const { data: session, status } = useSession();
    const pathname = usePathname();
    const router = useRouter();

    const isPublic = publicRoutes.includes(pathname);

    useEffect(() => {
        if (!isPublic && status === "unauthenticated") {
            router.push("/login");
        }
    }, [status, pathname]);

    if (!isPublic && status === "loading") {
        return <div className="text-center p-10">Checking authentication...</div>;
    }

    return <>{children}</>;
}

export default function AuthProviderWrapper({ children, session }) {
    return (
        <SessionProvider session={session}>
            <AuthGuard>{children}</AuthGuard>
        </SessionProvider>
    );
}